# Space Marine 2 - PAK File Extraction for Modding
# This script helps extract the required PAK files for the mod editor

Write-Host "Space Marine 2 - PAK File Extraction" -ForegroundColor Green
Write-Host "====================================" -ForegroundColor Green
Write-Host ""

$pakFile = "client_pc\root\paks\client\default\default_other.pak"
$extractDir = "ModEditor\mods_source"

Write-Host "Checking for PAK file..." -ForegroundColor Yellow
if (Test-Path $pakFile) {
    $fileSize = (Get-Item $pakFile).Length / 1MB
    Write-Host "Found: $pakFile ($([math]::Round($fileSize, 2)) MB)" -ForegroundColor Green
} else {
    Write-Host "ERROR: PAK file not found at $pakFile" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Attempting extraction methods..." -ForegroundColor Yellow

# Method 1: Try as ZIP archive
Write-Host "Method 1: Trying ZIP extraction..." -ForegroundColor Cyan
try {
    Expand-Archive -Path $pakFile -DestinationPath $extractDir -Force -ErrorAction Stop
    Write-Host "SUCCESS: ZIP extraction worked!" -ForegroundColor Green
    $extracted = $true
} catch {
    Write-Host "ZIP extraction failed: $($_.Exception.Message)" -ForegroundColor Red
    $extracted = $false
}

# Method 2: Try with 7-Zip if available
if (-not $extracted) {
    Write-Host "Method 2: Trying 7-Zip extraction..." -ForegroundColor Cyan
    
    $sevenZipPaths = @(
        "C:\Program Files\7-Zip\7z.exe",
        "C:\Program Files (x86)\7-Zip\7z.exe",
        "${env:ProgramFiles}\7-Zip\7z.exe"
    )
    
    $sevenZip = $null
    foreach ($path in $sevenZipPaths) {
        if (Test-Path $path) {
            $sevenZip = $path
            break
        }
    }
    
    if ($sevenZip) {
        try {
            & $sevenZip x $pakFile "-o$extractDir" -y
            if ($LASTEXITCODE -eq 0) {
                Write-Host "SUCCESS: 7-Zip extraction worked!" -ForegroundColor Green
                $extracted = $true
            } else {
                Write-Host "7-Zip extraction failed with exit code: $LASTEXITCODE" -ForegroundColor Red
            }
        } catch {
            Write-Host "7-Zip extraction error: $($_.Exception.Message)" -ForegroundColor Red
        }
    } else {
        Write-Host "7-Zip not found. Install 7-Zip for better PAK support." -ForegroundColor Yellow
    }
}

# Method 3: Check if files already exist
if (-not $extracted) {
    Write-Host "Method 3: Checking for existing extracted files..." -ForegroundColor Cyan
    
    $existingFiles = Get-ChildItem -Path $extractDir -Recurse -File | Measure-Object
    if ($existingFiles.Count -gt 100) {
        Write-Host "Found $($existingFiles.Count) existing files in mod directory" -ForegroundColor Green
        Write-Host "Files may already be extracted!" -ForegroundColor Green
        $extracted = $true
    } else {
        Write-Host "Only $($existingFiles.Count) files found - extraction may be needed" -ForegroundColor Yellow
    }
}

Write-Host ""
if ($extracted) {
    Write-Host "EXTRACTION COMPLETE!" -ForegroundColor Green
    Write-Host "===================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Next steps:" -ForegroundColor Cyan
    Write-Host "1. Try launching IntegrationStudio.exe again" -ForegroundColor White
    Write-Host "2. Navigate to ModEditor\tools\ModEditor\" -ForegroundColor White
    Write-Host "3. Double-click IntegrationStudio.exe" -ForegroundColor White
    Write-Host "4. If it still doesn't work, try running as Administrator" -ForegroundColor White
    Write-Host ""
    
    # Check for firearms directory
    $firearmsDir = "$extractDir\ssl\firearms"
    if (Test-Path $firearmsDir) {
        Write-Host "✅ Firearms directory found!" -ForegroundColor Green
        $weaponFiles = Get-ChildItem -Path $firearmsDir -Recurse -File | Measure-Object
        Write-Host "   Contains $($weaponFiles.Count) weapon files" -ForegroundColor Gray
    } else {
        Write-Host "⚠️ Firearms directory not found - may need different PAK file" -ForegroundColor Yellow
    }
    
} else {
    Write-Host "EXTRACTION FAILED!" -ForegroundColor Red
    Write-Host "==================" -ForegroundColor Red
    Write-Host ""
    Write-Host "Possible solutions:" -ForegroundColor Yellow
    Write-Host "1. Install 7-Zip and try again" -ForegroundColor White
    Write-Host "2. The PAK file may use a proprietary format" -ForegroundColor White
    Write-Host "3. Try launching IntegrationStudio anyway - it may extract automatically" -ForegroundColor White
    Write-Host "4. Check if there are other PAK files to extract" -ForegroundColor White
}

Write-Host ""
Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
