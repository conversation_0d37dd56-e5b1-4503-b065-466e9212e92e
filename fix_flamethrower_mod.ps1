# Space Marine 2 - Fix Flamethrower Mod Loading

Write-Host "Fixing Flamethrower Mod Loading..." -ForegroundColor Green

# Configuration
$TempDir = "temp_mod_fix"
$SourceFile = "ModEditor\mods_source\ssl\weapons\flamethrower_pistol_complete.sso"
$PakFile = "client_pc\root\mods\flamethrower_pistol.pak"

# Clean temp directory
if (Test-Path $TempDir) {
    Remove-Item $TempDir -Recurse -Force
}

# Create mod structure
New-Item -ItemType Directory -Path "$TempDir\ssl\weapons" -Force | Out-Null

# Check source file
if (-not (Test-Path $SourceFile)) {
    Write-Host "ERROR: Source file not found: $SourceFile" -ForegroundColor Red
    exit 1
}

# Copy weapon file
Copy-Item $SourceFile "$TempDir\ssl\weapons\flamethrower_pistol_complete.sso" -Force
Write-Host "Copied weapon file to mod structure" -ForegroundColor Yellow

# Ensure mods directory exists
if (-not (Test-Path "client_pc\root\mods")) {
    New-Item -ItemType Directory -Path "client_pc\root\mods" -Force | Out-Null
}

# Remove old PAK
if (Test-Path $PakFile) {
    Remove-Item $PakFile -Force
}

# Create PAK file
try {
    Compress-Archive -Path "$TempDir\*" -DestinationPath $PakFile -Force
    Write-Host "PAK file created: $PakFile" -ForegroundColor Green
} catch {
    Write-Host "Failed to create PAK file: $_" -ForegroundColor Red
    exit 1
}

# Update PAK configuration
$PakConfig = "- pak: flamethrower_pistol.pak"
$PakConfig | Out-File "client_pc\root\mods\pak_config.yaml" -Encoding UTF8

# Also copy to local directory
$LocalDir = "client_pc\root\local\ssl\weapons"
if (-not (Test-Path $LocalDir)) {
    New-Item -ItemType Directory -Path $LocalDir -Force | Out-Null
}
Copy-Item $SourceFile "$LocalDir\flamethrower_pistol_complete.sso" -Force

# Clean up
Remove-Item $TempDir -Recurse -Force

Write-Host ""
Write-Host "FLAMETHROWER MOD FIXED!" -ForegroundColor Green
Write-Host "======================" -ForegroundColor Green
Write-Host ""
Write-Host "Files created:" -ForegroundColor Cyan
Write-Host "- $PakFile" -ForegroundColor White
Write-Host "- client_pc\root\mods\pak_config.yaml" -ForegroundColor White
Write-Host "- client_pc\root\local\ssl\weapons\flamethrower_pistol_complete.sso" -ForegroundColor White
Write-Host ""
Write-Host "IMPORTANT: RESTART Space Marine 2 to load the mod!" -ForegroundColor Red
Write-Host ""
Write-Host "Test Instructions:" -ForegroundColor Yellow
Write-Host "1. Close Space Marine 2 completely" -ForegroundColor White
Write-Host "2. Launch Space Marine 2" -ForegroundColor White
Write-Host "3. Start any mission" -ForegroundColor White
Write-Host "4. Equip bolt pistol" -ForegroundColor White
Write-Host "5. Should now be 1250 damage flamethrower!" -ForegroundColor White
Write-Host ""
