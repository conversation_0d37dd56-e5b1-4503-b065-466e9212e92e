@echo off
title Space Marine 2 - Extract Game Files for Modding
color 0A
echo.
echo  ███████╗██████╗  █████╗  ██████╗███████╗    ███╗   ███╗ █████╗ ██████╗ ██╗███╗   ██╗███████╗    ██████╗ 
echo  ██╔════╝██╔══██╗██╔══██╗██╔════╝██╔════╝    ████╗ ████║██╔══██╗██╔══██╗██║████╗  ██║██╔════╝    ╚════██╗
echo  ███████╗██████╔╝███████║██║     █████╗      ██╔████╔██║███████║██████╔╝██║██╔██╗ ██║█████╗       █████╔╝
echo  ╚════██║██╔═══╝ ██╔══██║██║     ██╔══╝      ██║╚██╔╝██║██╔══██║██╔══██╗██║██║╚██╗██║██╔══╝      ██╔═══╝ 
echo  ███████║██║     ██║  ██║╚██████╗███████╗    ██║ ╚═╝ ██║██║  ██║██║  ██║██║██║ ╚████║███████╗    ███████╗
echo  ╚══════╝╚═╝     ╚═╝  ╚═╝ ╚═════╝╚══════╝    ╚═╝     ╚═╝╚═╝  ╚═╝╚═╝  ╚═╝╚═╝╚═╝  ╚═══╝╚══════╝    ╚══════╝
echo.
echo                           FLAMETHROWER PISTOL MOD SETUP
echo                          ================================
echo.

echo [1/4] Checking for 7-Zip installation...
if exist "C:\Program Files\7-Zip\7z.exe" (
    set "SEVENZIP=C:\Program Files\7-Zip\7z.exe"
    echo ✓ Found 7-Zip at: Program Files
) else if exist "C:\Program Files (x86)\7-Zip\7z.exe" (
    set "SEVENZIP=C:\Program Files (x86)\7-Zip\7z.exe"
    echo ✓ Found 7-Zip at: Program Files (x86)
) else (
    echo ✗ ERROR: 7-Zip not found!
    echo.
    echo Please install 7-Zip first:
    echo 1. Go to: https://www.7-zip.org/
    echo 2. Download and install 7-Zip
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

echo.
echo [2/4] Extracting weapon configuration files...
echo Extracting default_other.pak...
"%SEVENZIP%" x "client_pc\root\paks\client\default\default_other.pak" -o"ModEditor\mods_source" -y >nul 2>&1

echo Extracting default_ssl.pak (weapon scripts)...
"%SEVENZIP%" x "client_pc\root\paks\client\default\default_ssl.pak" -o"ModEditor\mods_source" -y >nul 2>&1

echo.
echo [3/4] Verifying extraction...
if exist "ModEditor\mods_source\ssl\weapons" (
    echo ✓ Weapons folder extracted successfully!
) else (
    echo ⚠ Weapons folder not found - trying additional PAK files...
)

if exist "ModEditor\mods_source\ssl\firearms" (
    echo ✓ Firearms folder extracted successfully!
) else (
    echo ℹ Firearms folder not found (may be in different PAK file)
)

echo.
echo [4/4] Setup complete!
echo.
echo ✓ Game files extracted to: ModEditor\mods_source\
echo ✓ Ready to create flamethrower pistol mod!
echo.
echo NEXT STEPS:
echo 1. Launch IntegrationStudio.exe from: ModEditor\tools\ModEditor\
echo 2. Open bolt_pistol_flamethrower_mod.sso
echo 3. Modify weapon properties for flamethrower behavior
echo.
echo Press any key to launch IntegrationStudio...
pause >nul

echo Launching IntegrationStudio...
start "" "ModEditor\tools\ModEditor\IntegrationStudio.exe"

echo.
echo IntegrationStudio should now be opening...
echo The weapon files should now load properly!
echo.
pause
