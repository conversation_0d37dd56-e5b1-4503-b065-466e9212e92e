@echo off
echo Space Marine 2 - Modding Setup
echo ==============================
echo.

echo Step 1: Checking for 7-Zip...
if exist "C:\Program Files\7-Zip\7z.exe" (
    set SEVENZIP="C:\Program Files\7-Zip\7z.exe"
    echo Found 7-Zip at Program Files
) else if exist "C:\Program Files (x86)\7-Zip\7z.exe" (
    set SEVENZIP="C:\Program Files (x86)\7-Zip\7z.exe"
    echo Found 7-Zip at Program Files (x86)
) else (
    echo ERROR: 7-Zip not found!
    echo Please install 7-Zip from https://www.7-zip.org/
    echo Then run this script again.
    pause
    exit /b 1
)

echo.
echo Step 2: Extracting PAK files...
echo Extracting default_other.pak...
%SEVENZIP% x "client_pc\root\paks\client\default\default_other.pak" -o"ModEditor\mods_source" -y

echo.
echo Extracting default_ssl.pak...
%SEVENZIP% x "client_pc\root\paks\client\default\default_ssl.pak" -o"ModEditor\mods_source" -y

echo.
echo Step 3: Checking extraction...
if exist "ModEditor\mods_source\ssl\weapons" (
    echo SUCCESS: Weapons folder found!
) else (
    echo WARNING: Weapons folder not found
)

if exist "ModEditor\mods_source\ssl\firearms" (
    echo SUCCESS: Firearms folder found!
) else (
    echo INFO: Firearms folder not found (may be in different PAK)
)

echo.
echo Step 4: Ready to launch IntegrationStudio
echo Navigate to: ModEditor\tools\ModEditor\
echo Run: IntegrationStudio.exe
echo.
pause
