# Space Marine 2 - Simple Flamethrower Pistol Mod Builder

Write-Host "Building Flamethrower Pistol Mod..." -ForegroundColor Green

# Configuration
$SourceFile = "ModEditor\mods_source\ssl\weapons\flamethrower_pistol_complete.sso"
$DestDir = "client_pc\root\local\ssl\weapons"
$DestFile = "$DestDir\flamethrower_pistol_complete.sso"

# Check source file
if (-not (Test-Path $SourceFile)) {
    Write-Host "ERROR: Source file not found: $SourceFile" -ForegroundColor Red
    exit 1
}

Write-Host "Source file found: $SourceFile" -ForegroundColor Yellow

# Create destination directory
if (-not (Test-Path $DestDir)) {
    New-Item -ItemType Directory -Path $DestDir -Force | Out-Null
    Write-Host "Created directory: $DestDir" -ForegroundColor Yellow
}

# Copy file
Copy-Item $SourceFile $DestFile -Force
Write-Host "Installed mod file: $DestFile" -ForegroundColor Green

# Create mod info
$ModInfo = @{
    Name = "Flamethrower Pistol Mod"
    Version = "1.0.0"
    InstallDate = Get-Date -Format "yyyy-MM-dd HH:mm:ss"
    Features = @(
        "1250 base damage (exceeds 999 requirement)",
        "Continuous flame stream projectiles",
        "Fire particle effects",
        "Flamethrower audio"
    )
    Description = "Transforms bolt pistol into overpowered flamethrower"
}

$ModInfo | ConvertTo-Json | Out-File "flamethrower_pistol_mod_info.json" -Encoding UTF8

Write-Host ""
Write-Host "FLAMETHROWER PISTOL MOD INSTALLED!" -ForegroundColor Green
Write-Host "===================================" -ForegroundColor Green
Write-Host ""
Write-Host "Mod Features:" -ForegroundColor Cyan
Write-Host "- Damage: 1250 (exceeds 999+ requirement)" -ForegroundColor White
Write-Host "- Projectile: flame_stream type" -ForegroundColor White
Write-Host "- Visual Effects: fire particles" -ForegroundColor White
Write-Host "- Audio Effects: flamethrower sounds" -ForegroundColor White
Write-Host ""
Write-Host "Ready to test in Space Marine 2!" -ForegroundColor Yellow
Write-Host "1. Launch the game" -ForegroundColor White
Write-Host "2. Equip bolt pistol" -ForegroundColor White
Write-Host "3. Enjoy overpowered flamethrower!" -ForegroundColor White
Write-Host ""
