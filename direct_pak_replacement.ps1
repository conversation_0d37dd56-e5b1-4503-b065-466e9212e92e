# Space Marine 2 - Direct PAK Replacement Method
# This method directly modifies the main game PAK files

Write-Host "DIRECT PAK REPLACEMENT METHOD" -ForegroundColor Red
Write-Host "=============================" -ForegroundColor Red
Write-Host ""
Write-Host "WARNING: This method directly modifies game files!" -ForegroundColor Yellow
Write-Host "A backup will be created automatically." -ForegroundColor Yellow
Write-Host ""

# Configuration
$MainPak = "client_pc\root\paks\client\default\default.pak"
$BackupPak = "client_pc\root\paks\client\default\default.pak.flamethrower_backup"
$SourceFile = "ModEditor\mods_source\ssl\weapons\flamethrower_pistol_complete.sso"
$TempDir = "temp_pak_replacement"

# Check if main PAK exists
if (-not (Test-Path $MainPak)) {
    Write-Host "ERROR: Main PAK file not found: $MainPak" -ForegroundColor Red
    exit 1
}

# Create backup if it doesn't exist
if (-not (Test-Path $BackupPak)) {
    Write-Host "Creating backup of original PAK file..." -ForegroundColor Yellow
    Copy-Item $MainPak $BackupPak -Force
    Write-Host "Backup created: $BackupPak" -ForegroundColor Green
} else {
    Write-Host "Backup already exists: $BackupPak" -ForegroundColor Green
}

# Check source file
if (-not (Test-Path $SourceFile)) {
    Write-Host "ERROR: Source file not found: $SourceFile" -ForegroundColor Red
    exit 1
}

Write-Host ""
Write-Host "Extracting main PAK file..." -ForegroundColor Yellow

# Clean temp directory
if (Test-Path $TempDir) {
    Remove-Item $TempDir -Recurse -Force
}

# Try to extract the main PAK file using 7-Zip
$SevenZip = "C:\Program Files\7-Zip\7z.exe"
if (-not (Test-Path $SevenZip)) {
    $SevenZip = "C:\Program Files (x86)\7-Zip\7z.exe"
}

if (Test-Path $SevenZip) {
    try {
        & $SevenZip x $MainPak "-o$TempDir" -y | Out-Null
        Write-Host "PAK file extracted successfully" -ForegroundColor Green
    } catch {
        Write-Host "Failed to extract PAK file with 7-Zip: $_" -ForegroundColor Red
        exit 1
    }
} else {
    Write-Host "ERROR: 7-Zip not found. Cannot extract PAK file." -ForegroundColor Red
    Write-Host "Please install 7-Zip from https://www.7-zip.org/" -ForegroundColor Yellow
    exit 1
}

# Create weapons directory if it doesn't exist
$WeaponsDir = "$TempDir\ssl\weapons"
if (-not (Test-Path $WeaponsDir)) {
    New-Item -ItemType Directory -Path $WeaponsDir -Force | Out-Null
}

# Copy our flamethrower mod into the extracted PAK
Copy-Item $SourceFile "$WeaponsDir\flamethrower_pistol_complete.sso" -Force
Write-Host "Injected flamethrower mod into PAK contents" -ForegroundColor Green

Write-Host ""
Write-Host "Rebuilding PAK file with modifications..." -ForegroundColor Yellow

# Remove the original PAK file
Remove-Item $MainPak -Force

# Rebuild the PAK file
try {
    & $SevenZip a -tzip $MainPak "$TempDir\*" -r | Out-Null
    Write-Host "PAK file rebuilt successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to rebuild PAK file: $_" -ForegroundColor Red
    Write-Host "Restoring backup..." -ForegroundColor Yellow
    Copy-Item $BackupPak $MainPak -Force
    exit 1
}

# Clean up temp directory
Remove-Item $TempDir -Recurse -Force

# Verify the new PAK file
if (Test-Path $MainPak) {
    $pakSize = (Get-Item $MainPak).Length / 1MB
    Write-Host ""
    Write-Host "SUCCESS: DIRECT PAK REPLACEMENT COMPLETE!" -ForegroundColor Green
    Write-Host "=========================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "Modified PAK: $MainPak" -ForegroundColor Cyan
    Write-Host "Size: $([math]::Round($pakSize, 2)) MB" -ForegroundColor Gray
    Write-Host "Backup: $BackupPak" -ForegroundColor Gray
    Write-Host ""
    Write-Host "FLAMETHROWER MOD INJECTED DIRECTLY INTO GAME!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Test Instructions:" -ForegroundColor Yellow
    Write-Host "1. Launch Space Marine 2" -ForegroundColor White
    Write-Host "2. Start any mission" -ForegroundColor White
    Write-Host "3. Equip bolt pistol" -ForegroundColor White
    Write-Host "4. Should now be 1250 damage flamethrower!" -ForegroundColor White
    Write-Host ""
    Write-Host "To restore original game:" -ForegroundColor Cyan
    Write-Host "Copy $BackupPak to $MainPak" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "ERROR: PAK file rebuild failed!" -ForegroundColor Red
    exit 1
}
