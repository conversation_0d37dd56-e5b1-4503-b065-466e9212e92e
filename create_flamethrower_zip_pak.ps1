# Space Marine 2 - Create Flamethrower PAK (as ZIP)

Write-Host "Creating Flamethrower PAK file..." -ForegroundColor Green

# Configuration
$TempDir = "temp_mod_pak"
$SourceFile = "ModEditor\mods_source\ssl\weapons\flamethrower_pistol_complete.sso"
$ZipFile = "client_pc\root\mods\flamethrower_pistol.zip"
$PakFile = "client_pc\root\mods\flamethrower_pistol.pak"

# Clean temp directory
if (Test-Path $TempDir) {
    Remove-Item $TempDir -Recurse -Force
}

# Create mod structure
New-Item -ItemType Directory -Path "$TempDir\ssl\weapons" -Force | Out-Null

# Check source file
if (-not (Test-Path $SourceFile)) {
    Write-Host "ERROR: Source file not found: $SourceFile" -ForegroundColor Red
    exit 1
}

# Copy weapon file
Copy-Item $SourceFile "$TempDir\ssl\weapons\flamethrower_pistol_complete.sso" -Force
Write-Host "Copied weapon file to mod structure" -ForegroundColor Yellow

# Ensure mods directory exists
if (-not (Test-Path "client_pc\root\mods")) {
    New-Item -ItemType Directory -Path "client_pc\root\mods" -Force | Out-Null
}

# Remove old files
if (Test-Path $ZipFile) { Remove-Item $ZipFile -Force }
if (Test-Path $PakFile) { Remove-Item $PakFile -Force }

# Create ZIP file first
try {
    Compress-Archive -Path "$TempDir\*" -DestinationPath $ZipFile -Force
    Write-Host "ZIP file created successfully" -ForegroundColor Green
} catch {
    Write-Host "Failed to create ZIP file: $_" -ForegroundColor Red
    exit 1
}

# Rename ZIP to PAK
try {
    Move-Item $ZipFile $PakFile -Force
    Write-Host "Renamed to PAK file: $PakFile" -ForegroundColor Green
} catch {
    Write-Host "Failed to rename to PAK: $_" -ForegroundColor Red
    exit 1
}

# Update PAK configuration
$PakConfig = "- pak: flamethrower_pistol.pak"
$PakConfig | Out-File "client_pc\root\mods\pak_config.yaml" -Encoding UTF8
Write-Host "Updated pak_config.yaml" -ForegroundColor Green

# Also install to local directory for immediate testing
$LocalDir = "client_pc\root\local\ssl\weapons"
if (-not (Test-Path $LocalDir)) {
    New-Item -ItemType Directory -Path $LocalDir -Force | Out-Null
}
Copy-Item $SourceFile "$LocalDir\flamethrower_pistol_complete.sso" -Force
Write-Host "Also installed to local directory" -ForegroundColor Green

# Clean up
Remove-Item $TempDir -Recurse -Force

# Verify PAK file
if (Test-Path $PakFile) {
    $pakSize = (Get-Item $PakFile).Length / 1KB
    Write-Host ""
    Write-Host "SUCCESS: FLAMETHROWER PAK CREATED!" -ForegroundColor Green
    Write-Host "==================================" -ForegroundColor Green
    Write-Host ""
    Write-Host "PAK file: $PakFile" -ForegroundColor Cyan
    Write-Host "Size: $([math]::Round($pakSize, 2)) KB" -ForegroundColor Gray
    Write-Host ""
    Write-Host "CRITICAL: RESTART Space Marine 2 to load the mod!" -ForegroundColor Red
    Write-Host ""
    Write-Host "Test Steps:" -ForegroundColor Yellow
    Write-Host "1. Close Space Marine 2 completely" -ForegroundColor White
    Write-Host "2. Launch Space Marine 2" -ForegroundColor White
    Write-Host "3. Start any mission" -ForegroundColor White
    Write-Host "4. Equip bolt pistol" -ForegroundColor White
    Write-Host "5. Should now deal 1250 damage with flame effects!" -ForegroundColor White
    Write-Host ""
    Write-Host "Mod Features:" -ForegroundColor Cyan
    Write-Host "- 1250 base damage (exceeds 999 requirement)" -ForegroundColor White
    Write-Host "- Continuous flame stream projectiles" -ForegroundColor White
    Write-Host "- Fire particle visual effects" -ForegroundColor White
    Write-Host "- Flamethrower audio effects" -ForegroundColor White
    Write-Host ""
} else {
    Write-Host "ERROR: PAK file was not created!" -ForegroundColor Red
    exit 1
}
